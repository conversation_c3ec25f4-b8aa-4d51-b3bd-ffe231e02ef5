[{"E:\\FX\\Cyber POS\\cyber-pos-system\\src\\index.tsx": "1", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\reportWebVitals.js": "2", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\App.tsx": "3", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\contexts\\AuthContext.tsx": "4", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\dashboard\\Dashboard.tsx": "5", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\auth\\Login.tsx": "6", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\Inventory.tsx": "7", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\POS.tsx": "8", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\services\\Services.tsx": "9", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\reports\\Reports.tsx": "10", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\settings\\Settings.tsx": "11", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\layout\\Layout.tsx": "12", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\common\\LoadingSpinner.tsx": "13", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\offline\\OfflineManager.tsx": "14", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\config\\firebase.ts": "15", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useDashboard.ts": "16", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useProducts.ts": "17", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useServices.ts": "18", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useReports.ts": "19", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useCart.ts": "20", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\utils\\seedData.ts": "21", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\utils\\testDataSeeder.ts": "22", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\InventoryStats.tsx": "23", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\ProductModal.tsx": "24", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\StockAdjustmentModal.tsx": "25", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\LowStockAlert.tsx": "26", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\POSCart.tsx": "27", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\ServiceSelector.tsx": "28", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\ProductSelector.tsx": "29", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\services\\ServiceCategories.tsx": "30", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\services\\ServiceStats.tsx": "31", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\services\\ServiceModal.tsx": "32", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\reports\\InventoryAnalytics.tsx": "33", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\users\\UserManagement.tsx": "34", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useTransactions.ts": "35", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\utils\\receiptGenerator.ts": "36", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\utils\\serviceUtils.ts": "37", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\CheckoutModal.tsx": "38", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\BulkOperations.tsx": "39", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\utils\\excelImport.ts": "40", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\debug\\FirebaseConnectionTest.tsx": "41", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\utils\\firebaseConnectionTest.ts": "42"}, {"size": 550, "mtime": 1751002257124, "results": "43", "hashOfConfig": "44"}, {"size": 362, "mtime": 1751001516282, "results": "45", "hashOfConfig": "44"}, {"size": 2473, "mtime": 1751092380796, "results": "46", "hashOfConfig": "44"}, {"size": 5912, "mtime": 1751002889593, "results": "47", "hashOfConfig": "44"}, {"size": 9398, "mtime": 1751013933072, "results": "48", "hashOfConfig": "44"}, {"size": 7363, "mtime": 1751020900684, "results": "49", "hashOfConfig": "44"}, {"size": 17601, "mtime": 1751093334719, "results": "50", "hashOfConfig": "44"}, {"size": 8045, "mtime": 1751003816943, "results": "51", "hashOfConfig": "44"}, {"size": 13480, "mtime": 1751003649429, "results": "52", "hashOfConfig": "44"}, {"size": 26580, "mtime": 1751008212115, "results": "53", "hashOfConfig": "44"}, {"size": 8562, "mtime": 1751007249570, "results": "54", "hashOfConfig": "44"}, {"size": 6483, "mtime": 1751001825178, "results": "55", "hashOfConfig": "44"}, {"size": 665, "mtime": 1751001779724, "results": "56", "hashOfConfig": "44"}, {"size": 10403, "mtime": 1751017441838, "results": "57", "hashOfConfig": "44"}, {"size": 3887, "mtime": 1751019446562, "results": "58", "hashOfConfig": "44"}, {"size": 9454, "mtime": 1751013836375, "results": "59", "hashOfConfig": "44"}, {"size": 6079, "mtime": 1751003779872, "results": "60", "hashOfConfig": "44"}, {"size": 5279, "mtime": 1751092620848, "results": "61", "hashOfConfig": "44"}, {"size": 10667, "mtime": 1751006877684, "results": "62", "hashOfConfig": "44"}, {"size": 5107, "mtime": 1751003753867, "results": "63", "hashOfConfig": "44"}, {"size": 9579, "mtime": 1751020844856, "results": "64", "hashOfConfig": "44"}, {"size": 7956, "mtime": 1751007184267, "results": "65", "hashOfConfig": "44"}, {"size": 11510, "mtime": 1751009626258, "results": "66", "hashOfConfig": "44"}, {"size": 12558, "mtime": 1751004256417, "results": "67", "hashOfConfig": "44"}, {"size": 10775, "mtime": 1751004342324, "results": "68", "hashOfConfig": "44"}, {"size": 12479, "mtime": 1751004391960, "results": "69", "hashOfConfig": "44"}, {"size": 15195, "mtime": 1751005588991, "results": "70", "hashOfConfig": "44"}, {"size": 13401, "mtime": 1751003915007, "results": "71", "hashOfConfig": "44"}, {"size": 10910, "mtime": 1751003957303, "results": "72", "hashOfConfig": "44"}, {"size": 3964, "mtime": 1751003442458, "results": "73", "hashOfConfig": "44"}, {"size": 8498, "mtime": 1751003577995, "results": "74", "hashOfConfig": "44"}, {"size": 9054, "mtime": 1751003411814, "results": "75", "hashOfConfig": "44"}, {"size": 11145, "mtime": 1751009654679, "results": "76", "hashOfConfig": "44"}, {"size": 12716, "mtime": 1751002934527, "results": "77", "hashOfConfig": "44"}, {"size": 5054, "mtime": 1751005576405, "results": "78", "hashOfConfig": "44"}, {"size": 7154, "mtime": 1751017196047, "results": "79", "hashOfConfig": "44"}, {"size": 5162, "mtime": 1751003518513, "results": "80", "hashOfConfig": "44"}, {"size": 11914, "mtime": 1751004034012, "results": "81", "hashOfConfig": "44"}, {"size": 26303, "mtime": 1751093657648, "results": "82", "hashOfConfig": "44"}, {"size": 13824, "mtime": 1751026107868, "results": "83", "hashOfConfig": "44"}, {"size": 3407, "mtime": 1751030363309, "results": "84", "hashOfConfig": "44"}, {"size": 2725, "mtime": 1751030347462, "results": "85", "hashOfConfig": "44"}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "do8dzb", {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\index.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\reportWebVitals.js", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\App.tsx", ["212"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\contexts\\AuthContext.tsx", ["213"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\dashboard\\Dashboard.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\auth\\Login.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\Inventory.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\POS.tsx", ["214", "215", "216", "217", "218", "219", "220", "221"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\services\\Services.tsx", ["222", "223", "224", "225"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\reports\\Reports.tsx", ["226", "227", "228"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\settings\\Settings.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\layout\\Layout.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\common\\LoadingSpinner.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\offline\\OfflineManager.tsx", ["229", "230", "231"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\config\\firebase.ts", ["232"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useDashboard.ts", ["233", "234"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useProducts.ts", ["235", "236"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useServices.ts", ["237", "238"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useReports.ts", ["239", "240", "241", "242", "243", "244"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useCart.ts", ["245", "246"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\utils\\seedData.ts", ["247", "248", "249"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\utils\\testDataSeeder.ts", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\InventoryStats.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\ProductModal.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\StockAdjustmentModal.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\LowStockAlert.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\POSCart.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\ServiceSelector.tsx", ["250"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\ProductSelector.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\services\\ServiceCategories.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\services\\ServiceStats.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\services\\ServiceModal.tsx", ["251"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\reports\\InventoryAnalytics.tsx", ["252", "253"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\users\\UserManagement.tsx", ["254", "255"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useTransactions.ts", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\utils\\receiptGenerator.ts", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\utils\\serviceUtils.ts", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\CheckoutModal.tsx", ["256"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\BulkOperations.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\utils\\excelImport.ts", ["257"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\debug\\FirebaseConnectionTest.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\utils\\firebaseConnectionTest.ts", [], [], {"ruleId": "258", "severity": 1, "message": "259", "line": 18, "column": 7, "nodeType": "260", "messageId": "261", "endLine": 18, "endColumn": 62}, {"ruleId": "258", "severity": 1, "message": "262", "line": 16, "column": 3, "nodeType": "260", "messageId": "261", "endLine": 16, "endColumn": 8}, {"ruleId": "258", "severity": 1, "message": "263", "line": 10, "column": 3, "nodeType": "260", "messageId": "261", "endLine": 10, "endColumn": 7}, {"ruleId": "258", "severity": 1, "message": "264", "line": 11, "column": 3, "nodeType": "260", "messageId": "261", "endLine": 11, "endColumn": 13}, {"ruleId": "258", "severity": 1, "message": "265", "line": 12, "column": 3, "nodeType": "260", "messageId": "261", "endLine": 12, "endColumn": 10}, {"ruleId": "258", "severity": 1, "message": "266", "line": 13, "column": 3, "nodeType": "260", "messageId": "261", "endLine": 13, "endColumn": 9}, {"ruleId": "258", "severity": 1, "message": "267", "line": 14, "column": 3, "nodeType": "260", "messageId": "261", "endLine": 14, "endColumn": 7}, {"ruleId": "258", "severity": 1, "message": "268", "line": 15, "column": 3, "nodeType": "260", "messageId": "261", "endLine": 15, "endColumn": 8}, {"ruleId": "258", "severity": 1, "message": "269", "line": 21, "column": 10, "nodeType": "260", "messageId": "261", "endLine": 21, "endColumn": 17}, {"ruleId": "258", "severity": 1, "message": "270", "line": 21, "column": 19, "nodeType": "260", "messageId": "261", "endLine": 21, "endColumn": 26}, {"ruleId": "258", "severity": 1, "message": "271", "line": 9, "column": 3, "nodeType": "260", "messageId": "261", "endLine": 9, "endColumn": 13}, {"ruleId": "258", "severity": 1, "message": "272", "line": 10, "column": 3, "nodeType": "260", "messageId": "261", "endLine": 10, "endColumn": 6}, {"ruleId": "258", "severity": 1, "message": "273", "line": 14, "column": 3, "nodeType": "260", "messageId": "261", "endLine": 14, "endColumn": 4}, {"ruleId": "258", "severity": 1, "message": "274", "line": 15, "column": 3, "nodeType": "260", "messageId": "261", "endLine": 15, "endColumn": 7}, {"ruleId": "258", "severity": 1, "message": "275", "line": 11, "column": 3, "nodeType": "260", "messageId": "261", "endLine": 11, "endColumn": 9}, {"ruleId": "258", "severity": 1, "message": "276", "line": 26, "column": 10, "nodeType": "260", "messageId": "261", "endLine": 26, "endColumn": 13}, {"ruleId": "277", "severity": 1, "message": "278", "line": 57, "column": 6, "nodeType": "279", "endLine": 57, "endColumn": 22, "suggestions": "280"}, {"ruleId": "258", "severity": 1, "message": "281", "line": 6, "column": 3, "nodeType": "260", "messageId": "261", "endLine": 6, "endColumn": 14}, {"ruleId": "258", "severity": 1, "message": "282", "line": 7, "column": 3, "nodeType": "260", "messageId": "261", "endLine": 7, "endColumn": 14}, {"ruleId": "277", "severity": 1, "message": "283", "line": 51, "column": 6, "nodeType": "279", "endLine": 51, "endColumn": 27, "suggestions": "284"}, {"ruleId": "258", "severity": 1, "message": "285", "line": 8, "column": 3, "nodeType": "260", "messageId": "261", "endLine": 8, "endColumn": 23}, {"ruleId": "258", "severity": 1, "message": "270", "line": 12, "column": 23, "nodeType": "260", "messageId": "261", "endLine": 12, "endColumn": 30}, {"ruleId": "277", "severity": 1, "message": "286", "line": 300, "column": 6, "nodeType": "279", "endLine": 300, "endColumn": 8, "suggestions": "287"}, {"ruleId": "258", "severity": 1, "message": "288", "line": 5, "column": 3, "nodeType": "260", "messageId": "261", "endLine": 5, "endColumn": 10}, {"ruleId": "258", "severity": 1, "message": "262", "line": 11, "column": 3, "nodeType": "260", "messageId": "261", "endLine": 11, "endColumn": 8}, {"ruleId": "258", "severity": 1, "message": "288", "line": 5, "column": 3, "nodeType": "260", "messageId": "261", "endLine": 5, "endColumn": 10}, {"ruleId": "258", "severity": 1, "message": "262", "line": 11, "column": 3, "nodeType": "260", "messageId": "261", "endLine": 11, "endColumn": 8}, {"ruleId": "258", "severity": 1, "message": "289", "line": 1, "column": 20, "nodeType": "260", "messageId": "261", "endLine": 1, "endColumn": 29}, {"ruleId": "258", "severity": 1, "message": "290", "line": 9, "column": 3, "nodeType": "260", "messageId": "261", "endLine": 9, "endColumn": 13}, {"ruleId": "258", "severity": 1, "message": "291", "line": 10, "column": 3, "nodeType": "260", "messageId": "261", "endLine": 10, "endColumn": 8}, {"ruleId": "258", "severity": 1, "message": "270", "line": 13, "column": 23, "nodeType": "260", "messageId": "261", "endLine": 13, "endColumn": 30}, {"ruleId": "258", "severity": 1, "message": "269", "line": 13, "column": 32, "nodeType": "260", "messageId": "261", "endLine": 13, "endColumn": 39}, {"ruleId": "258", "severity": 1, "message": "263", "line": 13, "column": 41, "nodeType": "260", "messageId": "261", "endLine": 13, "endColumn": 45}, {"ruleId": "258", "severity": 1, "message": "292", "line": 3, "column": 10, "nodeType": "260", "messageId": "261", "endLine": 3, "endColumn": 31}, {"ruleId": "277", "severity": 1, "message": "293", "line": 87, "column": 6, "nodeType": "279", "endLine": 87, "endColumn": 8, "suggestions": "294"}, {"ruleId": "258", "severity": 1, "message": "263", "line": 14, "column": 10, "nodeType": "260", "messageId": "261", "endLine": 14, "endColumn": 14}, {"ruleId": "258", "severity": 1, "message": "269", "line": 14, "column": 16, "nodeType": "260", "messageId": "261", "endLine": 14, "endColumn": 23}, {"ruleId": "258", "severity": 1, "message": "270", "line": 14, "column": 25, "nodeType": "260", "messageId": "261", "endLine": 14, "endColumn": 32}, {"ruleId": "258", "severity": 1, "message": "271", "line": 5, "column": 3, "nodeType": "260", "messageId": "261", "endLine": 5, "endColumn": 13}, {"ruleId": "258", "severity": 1, "message": "272", "line": 2, "column": 25, "nodeType": "260", "messageId": "261", "endLine": 2, "endColumn": 28}, {"ruleId": "258", "severity": 1, "message": "270", "line": 12, "column": 10, "nodeType": "260", "messageId": "261", "endLine": 12, "endColumn": 17}, {"ruleId": "277", "severity": 1, "message": "295", "line": 45, "column": 6, "nodeType": "279", "endLine": 45, "endColumn": 16, "suggestions": "296"}, {"ruleId": "258", "severity": 1, "message": "266", "line": 8, "column": 3, "nodeType": "260", "messageId": "261", "endLine": 8, "endColumn": 9}, {"ruleId": "277", "severity": 1, "message": "297", "line": 34, "column": 6, "nodeType": "279", "endLine": 34, "endColumn": 8, "suggestions": "298"}, {"ruleId": "258", "severity": 1, "message": "299", "line": 9, "column": 3, "nodeType": "260", "messageId": "261", "endLine": 9, "endColumn": 10}, {"ruleId": "258", "severity": 1, "message": "300", "line": 21, "column": 7, "nodeType": "260", "messageId": "261", "endLine": 21, "endColumn": 23}, "@typescript-eslint/no-unused-vars", "'ProtectedRoute' is assigned a value but never used.", "Identifier", "unusedVar", "'where' is defined but never used.", "'User' is defined but never used.", "'CreditCard' is defined but never used.", "'Receipt' is defined but never used.", "'Trash2' is defined but never used.", "'Plus' is defined but never used.", "'Minus' is defined but never used.", "'Service' is defined but never used.", "'Product' is defined but never used.", "'DollarSign' is defined but never used.", "'Tag' is defined but never used.", "'X' is defined but never used.", "'Save' is defined but never used.", "'Filter' is defined but never used.", "'Bar' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadReport'. Either include it or remove the dependency array.", "ArrayExpression", ["301"], "'AlertCircle' is defined but never used.", "'CheckCircle' is defined but never used.", "React Hook useEffect has a missing dependency: 'syncOfflineData'. Either include it or remove the dependency array.", ["302"], "'persistentLocalCache' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadDashboardData'. Either include it or remove the dependency array.", ["303"], "'getDocs' is defined but never used.", "'useEffect' is defined but never used.", "'startAfter' is defined but never used.", "'limit' is defined but never used.", "'calculateServicePrice' is defined but never used.", "React Hook useCallback has a missing dependency: 'removeFromCart'. Either include it or remove the dependency array.", ["304"], "React Hook useEffect has missing dependencies: 'calculateMetrics' and 'generateAlerts'. Either include them or remove the dependency array.", ["305"], "React Hook useEffect has a missing dependency: 'loadUsers'. Either include it or remove the dependency array.", ["306"], "'Printer' is defined but never used.", "'OPTIONAL_COLUMNS' is assigned a value but never used.", {"desc": "307", "fix": "308"}, {"desc": "309", "fix": "310"}, {"desc": "311", "fix": "312"}, {"desc": "313", "fix": "314"}, {"desc": "315", "fix": "316"}, {"desc": "317", "fix": "318"}, "Update the dependencies array to be: [loadReport, selectedPeriod]", {"range": "319", "text": "320"}, "Update the dependencies array to be: [offlineQueue.length, syncOfflineData]", {"range": "321", "text": "322"}, "Update the dependencies array to be: [loadDashboardData]", {"range": "323", "text": "324"}, "Update the dependencies array to be: [removeFromCart]", {"range": "325", "text": "326"}, "Update the dependencies array to be: [calculateMetrics, generateAlerts, products]", {"range": "327", "text": "328"}, "Update the dependencies array to be: [loadUsers]", {"range": "329", "text": "330"}, [1381, 1397], "[load<PERSON><PERSON><PERSON>, selected<PERSON><PERSON><PERSON>]", [1357, 1378], "[offlineQueue.length, syncOfflineData]", [9246, 9248], "[loadDashboardData]", [2571, 2573], "[remove<PERSON><PERSON><PERSON><PERSON>]", [1163, 1173], "[calculateMetrics, generateAlerts, products]", [865, 867], "[loadUsers]"]